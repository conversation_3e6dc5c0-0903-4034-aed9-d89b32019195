version: '3.8'

services:
  # Reverse Proxy / Load Balancer
  nginx:
    image: nginx:alpine
    container_name: cherish-nginx-local
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.local.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - api
      - frontend
    networks:
      - cherish-network
    restart: unless-stopped

  # Backend API
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: cherish-api-local
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=cherish;Username=${POSTGRES_USER:-cherish_user};Password=${POSTGRES_PASSWORD:-cherish123}
      - Database__Provider=postgresql
      - Jwt__Key=${JWT_SECRET_KEY:-YourLocalDevelopmentSecretKeyHere123456789}
      - Jwt__Issuer=Cherish
      - Jwt__Audience=CherishClients
      - Jwt__ExpiryInMinutes=60
      - Jwt__RefreshTokenExpiryInDays=7
      - Authentication__Google__ClientId=${GOOGLE_CLIENT_ID:-your-google-client-id}
      - Authentication__Google__ClientSecret=${GOOGLE_CLIENT_SECRET:-your-google-client-secret}
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - cherish-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.local
      args:
        - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost/api}
        - NEXT_PUBLIC_GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID:-your-google-client-id}
    container_name: cherish-frontend-local
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost/api}
      - NEXT_PUBLIC_GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID:-your-google-client-id}
    depends_on:
      - api
    networks:
      - cherish-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Database
  postgres:
    image: postgres:16-alpine
    container_name: cherish-postgres-local
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-cherish_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-cherish123}
      - POSTGRES_DB=cherish
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5433:5433"  # Expose for local development
    volumes:
      - postgres-data-local:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups
    networks:
      - cherish-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-cherish_user} -d cherish"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: cherish-redis-local
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    ports:
      - "6379:6379"  # Expose for local development
    volumes:
      - redis-data-local:/data
    networks:
      - cherish-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Database Admin Tool (optional for local development)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: cherish-pgadmin-local
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "8080:80"
    volumes:
      - pgadmin-data-local:/var/lib/pgadmin
    networks:
      - cherish-network
    restart: unless-stopped
    depends_on:
      - postgres

networks:
  cherish-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres-data-local:
    driver: local
  redis-data-local:
    driver: local
  pgadmin-data-local:
    driver: local
