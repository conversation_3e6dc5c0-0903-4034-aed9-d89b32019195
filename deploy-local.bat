@echo off
REM Cherish Application Local Deployment Script for Windows
REM Simple batch file alternative to PowerShell script

setlocal enabledelayedexpansion

REM Configuration
set COMPOSE_FILE=docker-compose.local.yml
set ENV_FILE=.env.local

REM Colors (if supported)
set GREEN=[92m
set RED=[91m
set YELLOW=[93m
set BLUE=[94m
set NC=[0m

echo %BLUE%========================================%NC%
echo %BLUE%  Cherish Local Deployment (Windows)   %NC%
echo %BLUE%========================================%NC%
echo.

REM Check if Docker is installed
echo %BLUE%[INFO]%NC% Checking Docker installation...
docker --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Docker is not installed or not in PATH.
    echo Please install Docker Desktop for Windows from:
    echo https://www.docker.com/products/docker-desktop/
    pause
    exit /b 1
)

REM Check if Docker is running
echo %BLUE%[INFO]%NC% Checking if Docker is running...
docker info >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Docker is not running.
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

REM Check if environment file exists
if not exist "%ENV_FILE%" (
    echo %YELLOW%[WARNING]%NC% Environment file %ENV_FILE% not found.
    echo Creating from template...
    if exist ".env.local.example" (
        copy ".env.local.example" "%ENV_FILE%" >nul
        echo %GREEN%[SUCCESS]%NC% Environment file created: %ENV_FILE%
        echo %YELLOW%[INFO]%NC% Please edit %ENV_FILE% with your configuration.
        echo Press any key to continue after editing, or Ctrl+C to exit...
        pause >nul
    ) else (
        echo %RED%[ERROR]%NC% Template file .env.local.example not found.
        pause
        exit /b 1
    )
)

REM Create necessary directories
echo %BLUE%[INFO]%NC% Creating necessary directories...
if not exist "nginx\ssl" mkdir "nginx\ssl"
if not exist "nginx\logs" mkdir "nginx\logs"
if not exist "database\init" mkdir "database\init"
if not exist "database\backups" mkdir "database\backups"

REM Stop any existing services
echo %BLUE%[INFO]%NC% Stopping any existing services...
docker-compose -f %COMPOSE_FILE% --env-file %ENV_FILE% down >nul 2>&1

REM Pull latest images
echo %BLUE%[INFO]%NC% Pulling latest images...
docker-compose -f %COMPOSE_FILE% --env-file %ENV_FILE% pull

REM Build and start services
echo %BLUE%[INFO]%NC% Building and starting services...
docker-compose -f %COMPOSE_FILE% --env-file %ENV_FILE% up -d --build

REM Wait for services to start
echo %BLUE%[INFO]%NC% Waiting for services to start...
timeout /t 30 /nobreak >nul

REM Check service status
echo %BLUE%[INFO]%NC% Checking service status...
docker-compose -f %COMPOSE_FILE% --env-file %ENV_FILE% ps

REM Test health endpoint
echo %BLUE%[INFO]%NC% Testing application health...
timeout /t 10 /nobreak >nul
curl -s http://localhost/health >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% Health check failed. Services might still be starting...
    echo Check logs with: docker-compose -f %COMPOSE_FILE% logs
) else (
    echo %GREEN%[SUCCESS]%NC% Application is healthy!
)

echo.
echo %GREEN%========================================%NC%
echo %GREEN%     Deployment Completed!             %NC%
echo %GREEN%========================================%NC%
echo.
echo %BLUE%Access your application:%NC%
echo   Frontend:      http://localhost
echo   API:           http://localhost/api
echo   Health Check:  http://localhost/health
echo   Swagger Docs:  http://localhost/swagger
echo   Database Admin: http://localhost:8080
echo.
echo %BLUE%Database Connection (for external tools):%NC%
echo   Host:     localhost
echo   Port:     5433
echo   Username: cherish_user
echo   Password: cherish123
echo   Database: cherish
echo.
echo %BLUE%Useful Commands:%NC%
echo   View logs:     docker-compose -f %COMPOSE_FILE% logs
echo   Stop services: docker-compose -f %COMPOSE_FILE% down
echo   Restart:       docker-compose -f %COMPOSE_FILE% restart
echo.
echo %GREEN%Happy coding! 🚀%NC%
echo.
pause
